mod examples;

use aes::Aes128;
use cbc::{Decryptor, Encryptor};
use cbc::cipher::{KeyIvInit, BlockDecryptMut, BlockEncryptMut, block_padding::Pkcs7};
use hex;
use rand::Rng;

// 定义类型别名，使代码更简洁
type Aes128CbcEnc = Encryptor<Aes128>;
type Aes128CbcDec = Decryptor<Aes128>;

/// AES-128-CBC 加密函数
///
/// # 参数
/// * `key` - 16字节的加密密钥
/// * `plaintext` - 要加密的明文数据
///
/// # 返回值
/// * `(Vec<u8>, Vec<u8>)` - (IV, 密文) 元组
fn aes128_cbc_encrypt(key: &[u8; 16], plaintext: &[u8]) -> (Vec<u8>, Vec<u8>) {
    // 生成随机16字节IV
    let mut iv = [0u8; 16];
    rand::rng().fill(&mut iv);

    // 创建加密器
    let cipher = Aes128CbcEnc::new(key.into(), &iv.into());

    // 准备缓冲区（需要额外空间用于填充）
    let mut buffer = vec![0u8; plaintext.len() + 16]; // 最多需要16字节填充
    buffer[..plaintext.len()].copy_from_slice(plaintext);

    // 加密并自动进行PKCS#7填充
    let ciphertext_len = cipher.encrypt_padded_mut::<Pkcs7>(&mut buffer, plaintext.len())
        .expect("加密失败").len();

    buffer.truncate(ciphertext_len);
    (iv.to_vec(), buffer)
}

/// AES-128-CBC 解密函数
///
/// # 参数
/// * `key` - 16字节的解密密钥
/// * `iv` - 16字节的初始化向量
/// * `ciphertext` - 要解密的密文数据
///
/// # 返回值
/// * `Result<Vec<u8>, String>` - 解密后的明文或错误信息
fn aes128_cbc_decrypt(key: &[u8; 16], iv: &[u8; 16], ciphertext: &[u8]) -> Result<Vec<u8>, String> {
    // 创建解密器
    let cipher = Aes128CbcDec::new(key.into(), iv.into());

    // 准备缓冲区
    let mut buffer = ciphertext.to_vec();

    // 解密并自动去除PKCS#7填充
    let plaintext = cipher.decrypt_padded_mut::<Pkcs7>(&mut buffer)
        .map_err(|e| format!("解密失败: {:?}", e))?;

    Ok(plaintext.to_vec())
}

fn main() {
    println!("=== AES-128-CBC 加解密演示 ===\n");

    // 1. 准备测试数据
    let key = b"my_secret_key123"; // 16字节密钥
    let plaintext = b"Hello, AES-128-CBC! This is a test message for encryption.";

    println!("原始密钥: {}", hex::encode(key));
    println!("原始明文: {}", String::from_utf8_lossy(plaintext));
    println!("明文长度: {} 字节\n", plaintext.len());

    // 2. 加密
    println!("--- 加密过程 ---");
    let (iv, ciphertext) = aes128_cbc_encrypt(key, plaintext);

    println!("生成的IV: {}", hex::encode(&iv));
    println!("密文(hex): {}", hex::encode(&ciphertext));
    println!("密文长度: {} 字节", ciphertext.len());

    // 解释填充
    let padding_bytes = ciphertext.len() - plaintext.len();
    println!("填充字节数: {} (PKCS#7自动填充)\n", padding_bytes);

    // 3. 解密
    println!("--- 解密过程 ---");
    let iv_array: [u8; 16] = iv.try_into().unwrap();

    match aes128_cbc_decrypt(key, &iv_array, &ciphertext) {
        Ok(decrypted) => {
            println!("解密成功!");
            println!("解密后明文: {}", String::from_utf8_lossy(&decrypted));
            println!("解密后长度: {} 字节", decrypted.len());

            // 验证解密结果
            if decrypted == plaintext {
                println!("✅ 解密结果与原始明文完全一致!");
            } else {
                println!("❌ 解密结果与原始明文不一致!");
            }
        }
        Err(e) => {
            println!("❌ {}", e);
        }
    }

    println!("\n--- 技术要点说明 ---");
    println!("1. AES-128使用128位(16字节)密钥");
    println!("2. CBC模式需要16字节随机IV");
    println!("3. PKCS#7自动处理填充和去填充");
    println!("4. 每次加密IV都不同，相同明文产生不同密文");
    println!("5. 解密时需要相同的密钥和IV");

    // 运行详细示例
    examples::demo_different_lengths();
    examples::demo_padding_explanation();
    examples::demo_iv_importance();
    examples::demo_error_handling();
}
