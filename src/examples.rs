use aes::Aes128;
use cbc::{Decryptor, Encryptor};
use cbc::cipher::{KeyIvInit, BlockDecryptMut, BlockEncryptMut, block_padding::Pkcs7};
use hex;
use rand::Rng;

type Aes128CbcEnc = Encryptor<Aes128>;
type Aes128CbcDec = Decryptor<Aes128>;

/// 演示不同长度文本的加密
pub fn demo_different_lengths() {
    println!("\n=== 不同长度文本加密演示 ===");
    
    let key = b"my_secret_key123";
    let test_cases = vec![
        "A",                                    // 1字节
        "Hello World!",                         // 12字节
        "This is exactly 16!",                  // 16字节 (正好一个块)
        "This message is longer than 16 bytes", // 32字节 (两个块)
        "这是中文测试内容，包含多字节字符。",        // UTF-8编码
    ];
    
    for (i, plaintext) in test_cases.iter().enumerate() {
        println!("\n--- 测试案例 {} ---", i + 1);
        let plaintext_bytes = plaintext.as_bytes();
        println!("原文: {}", plaintext);
        println!("字节长度: {}", plaintext_bytes.len());
        
        let (iv, ciphertext) = encrypt(key, plaintext_bytes);
        println!("密文长度: {} (填充后)", ciphertext.len());
        println!("填充字节: {}", ciphertext.len() - plaintext_bytes.len());
        
        // 解密验证
        let iv_array: [u8; 16] = iv.try_into().unwrap();
        match decrypt(key, &iv_array, &ciphertext) {
            Ok(decrypted) => {
                let decrypted_text = String::from_utf8_lossy(&decrypted);
                println!("解密结果: {}", decrypted_text);
                println!("验证: {}", if decrypted == plaintext_bytes { "✅ 成功" } else { "❌ 失败" });
            }
            Err(e) => println!("解密失败: {}", e),
        }
    }
}

/// 演示PKCS#7填充的工作原理
pub fn demo_padding_explanation() {
    println!("\n=== PKCS#7 填充原理演示 ===");
    
    let key = b"my_secret_key123";
    
    // 测试不同的填充情况
    let test_cases = vec![
        ("15 bytes text!", 1),   // 需要1字节填充
        ("Exactly16bytes!", 16), // 需要16字节填充（整个新块）
        ("17 bytes text!!", 15), // 需要15字节填充
    ];
    
    for (text, expected_padding) in test_cases {
        println!("\n原文: \"{}\" ({} 字节)", text, text.len());
        println!("预期填充: {} 字节", expected_padding);
        
        let (_, ciphertext) = encrypt(key, text.as_bytes());
        let actual_padding = ciphertext.len() - text.len();
        println!("实际填充: {} 字节", actual_padding);
        println!("密文长度: {} 字节", ciphertext.len());
        
        // 解释填充规则
        let blocks_needed = (text.len() + 15) / 16; // 向上取整
        let total_bytes = blocks_needed * 16;
        println!("块数: {} × 16 = {} 字节", blocks_needed, total_bytes);
    }
    
    println!("\n📝 PKCS#7填充规则:");
    println!("- 如果数据长度是16的倍数，添加完整的16字节填充块");
    println!("- 填充字节的值等于填充的字节数");
    println!("- 例如：需要3字节填充，则添加 [03, 03, 03]");
}

/// 演示IV的重要性
pub fn demo_iv_importance() {
    println!("\n=== IV (初始化向量) 重要性演示 ===");
    
    let key = b"my_secret_key123";
    let plaintext = b"Same message, different results!";
    
    println!("相同明文和密钥，不同IV产生不同密文:");
    println!("明文: {}", String::from_utf8_lossy(plaintext));
    
    // 加密同样的消息3次
    for i in 1..=3 {
        let (iv, ciphertext) = encrypt(key, plaintext);
        println!("\n第{}次加密:", i);
        println!("IV:   {}", hex::encode(&iv));
        println!("密文: {}", hex::encode(&ciphertext));
    }
    
    println!("\n🔒 安全要点:");
    println!("- IV必须是随机的，每次加密都不同");
    println!("- IV可以公开，但不能重复使用");
    println!("- 相同的明文+密钥+IV会产生相同的密文");
    println!("- 不同的IV确保相同明文产生不同密文");
}

/// 演示错误处理
pub fn demo_error_handling() {
    println!("\n=== 错误处理演示 ===");
    
    let key = b"my_secret_key123";
    let iv = [0u8; 16];
    
    // 1. 无效密文长度（不是16的倍数）
    println!("\n1. 无效密文长度测试:");
    let invalid_ciphertext = vec![0u8; 15]; // 15字节，不是16的倍数
    match decrypt(key, &iv, &invalid_ciphertext) {
        Ok(_) => println!("意外成功"),
        Err(e) => println!("预期错误: {}", e),
    }
    
    // 2. 无效填充
    println!("\n2. 无效填充测试:");
    let mut invalid_padding = vec![0u8; 16];
    invalid_padding[15] = 17; // 填充值17超过了块大小16
    match decrypt(key, &iv, &invalid_padding) {
        Ok(_) => println!("意外成功"),
        Err(e) => println!("预期错误: {}", e),
    }
    
    println!("\n⚠️  常见错误:");
    println!("- 密文长度必须是16字节的倍数");
    println!("- 填充字节值必须有效（1-16）");
    println!("- 密钥和IV长度必须正确");
}

// 辅助函数
fn encrypt(key: &[u8; 16], plaintext: &[u8]) -> (Vec<u8>, Vec<u8>) {
    let mut iv = [0u8; 16];
    rand::rng().fill(&mut iv);
    
    let cipher = Aes128CbcEnc::new(key.into(), &iv.into());
    let mut buffer = vec![0u8; plaintext.len() + 16];
    buffer[..plaintext.len()].copy_from_slice(plaintext);
    
    let ciphertext_len = cipher.encrypt_padded_mut::<Pkcs7>(&mut buffer, plaintext.len())
        .expect("加密失败").len();
    
    buffer.truncate(ciphertext_len);
    (iv.to_vec(), buffer)
}

fn decrypt(key: &[u8; 16], iv: &[u8; 16], ciphertext: &[u8]) -> Result<Vec<u8>, String> {
    let cipher = Aes128CbcDec::new(key.into(), iv.into());
    let mut buffer = ciphertext.to_vec();
    
    let plaintext = cipher.decrypt_padded_mut::<Pkcs7>(&mut buffer)
        .map_err(|e| format!("解密失败: {:?}", e))?;
    
    Ok(plaintext.to_vec())
}
