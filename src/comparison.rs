// 你提供的AES-256-CBC实现
use aes::cipher::block_padding::Pkcs7;
use anyhow::anyhow;
use cbc::cipher::{BlockDecryptMut, BlockEncryptMut, KeyIvInit};

type Aes256CbcEnc = cbc::Encryptor<aes::Aes256>;
type Aes256CbcDec = cbc::Decryptor<aes::Aes256>;

const AES_KEY: [u8; 32] = *b"********************************";
const AES_IV: [u8; 16] = *b"45154c1fc4541161";

/// aes cbc pkcs7 enc
pub fn enc(input: &[u8]) -> anyhow::Result<Vec<u8>> {
    let cipher = Aes256CbcEnc::new(&AES_KEY.into(), &AES_IV.into());
    let mut buffer = vec![0u8; input.len() + 16];
    buffer[..input.len()].copy_from_slice(input);

    let ciphertext_len = cipher.encrypt_padded_mut::<Pkcs7>(&mut buffer, input.len())
        .map_err(|e| anyhow!("加密失败: {:?}", e))?.len();

    buffer.truncate(ciphertext_len);
    Ok(buffer)
}

/// aes cbc pkcs7 dec
pub fn dec(cipher: &[u8]) -> anyhow::Result<Vec<u8>> {
    let decryptor = Aes256CbcDec::new(&AES_KEY.into(), &AES_IV.into());
    let mut buffer = cipher.to_vec();

    let plaintext = decryptor.decrypt_padded_mut::<Pkcs7>(&mut buffer)
        .map_err(|e| anyhow!("解密失败: {:?}", e))?;

    Ok(plaintext.to_vec())
}

// 我们之前的AES-128-CBC实现（改进版）
use rand::Rng;

type Aes128CbcEnc = cbc::Encryptor<aes::Aes128>;
type Aes128CbcDec = cbc::Decryptor<aes::Aes128>;

pub fn our_encrypt(key: &[u8; 16], plaintext: &[u8]) -> (Vec<u8>, Vec<u8>) {
    let mut iv = [0u8; 16];
    rand::rng().fill(&mut iv);
    
    let cipher = Aes128CbcEnc::new(key.into(), &iv.into());
    let mut buffer = vec![0u8; plaintext.len() + 16];
    buffer[..plaintext.len()].copy_from_slice(plaintext);
    
    let ciphertext_len = cipher.encrypt_padded_mut::<Pkcs7>(&mut buffer, plaintext.len())
        .expect("加密失败").len();
    
    buffer.truncate(ciphertext_len);
    (iv.to_vec(), buffer)
}

pub fn our_decrypt(key: &[u8; 16], iv: &[u8; 16], ciphertext: &[u8]) -> Result<Vec<u8>, String> {
    let cipher = Aes128CbcDec::new(key.into(), iv.into());
    let mut buffer = ciphertext.to_vec();
    
    let plaintext = cipher.decrypt_padded_mut::<Pkcs7>(&mut buffer)
        .map_err(|e| format!("解密失败: {:?}", e))?;
    
    Ok(plaintext.to_vec())
}

/// 对比分析和演示
pub fn comparison_demo() {
    println!("\n=== AES实现对比分析 ===\n");
    
    // 测试数据
    let plaintext = b"Hello, this is a test message for AES comparison!";
    let key_128 = b"test_key_16bytes";
    
    println!("📊 **实现对比表格**");
    println!("┌─────────────────┬─────────────────┬─────────────────┐");
    println!("│ 特性            │ 你的实现        │ 我们的实现      │");
    println!("├─────────────────┼─────────────────┼─────────────────┤");
    println!("│ AES密钥长度     │ 256位(32字节)   │ 128位(16字节)   │");
    println!("│ 安全强度        │ 更高            │ 标准            │");
    println!("│ 性能            │ 稍慢            │ 更快            │");
    println!("│ IV处理          │ 固定IV          │ 随机IV          │");
    println!("│ 安全性          │ ⚠️ 有风险        │ ✅ 安全          │");
    println!("│ 代码简洁性      │ ✅ 非常简洁      │ 稍复杂          │");
    println!("│ 错误处理        │ anyhow          │ 自定义          │");
    println!("│ API设计         │ 全局常量        │ 参数传递        │");
    println!("└─────────────────┴─────────────────┴─────────────────┘");
    
    println!("\n🔍 **详细分析**\n");
    
    // 1. 安全性分析
    println!("1️⃣ **安全性对比**");
    println!("   你的实现:");
    println!("   ❌ 固定IV - 这是严重的安全漏洞!");
    println!("   ❌ 相同明文总是产生相同密文");
    println!("   ❌ 容易受到模式分析攻击");
    println!("   ✅ AES-256提供更强的密钥安全性");
    
    println!("\n   我们的实现:");
    println!("   ✅ 随机IV - 每次加密都不同");
    println!("   ✅ 相同明文产生不同密文");
    println!("   ✅ 抵抗模式分析攻击");
    println!("   ⚠️ AES-128在当前仍然安全");
    
    // 2. 演示固定IV的问题
    println!("\n2️⃣ **固定IV问题演示**");
    println!("   使用你的实现加密相同消息3次:");
    
    for i in 1..=3 {
        let ciphertext = enc(plaintext).unwrap();
        println!("   第{}次: {}", i, hex::encode(&ciphertext[..32])); // 只显示前32字节
    }
    
    println!("\n   ⚠️ 注意：密文完全相同！这暴露了信息模式");
    
    println!("\n   使用我们的实现加密相同消息3次:");
    for i in 1..=3 {
        let (iv, ciphertext) = our_encrypt(key_128, plaintext);
        println!("   第{}次: IV={} 密文={}", i, 
                hex::encode(&iv[..8]), 
                hex::encode(&ciphertext[..16]));
    }
    println!("   ✅ 每次都不同，保护了信息模式");
    
    // 3. 性能对比
    println!("\n3️⃣ **性能对比**");
    println!("   AES-256 vs AES-128:");
    println!("   • AES-256: 14轮加密，更多计算");
    println!("   • AES-128: 10轮加密，更快速度");
    println!("   • 对于大多数应用，AES-128已足够安全");
    
    // 4. 代码质量
    println!("\n4️⃣ **代码质量对比**");
    println!("   你的实现优点:");
    println!("   ✅ 代码极其简洁");
    println!("   ✅ 使用anyhow统一错误处理");
    println!("   ✅ API简单易用");
    
    println!("\n   你的实现缺点:");
    println!("   ❌ 硬编码密钥和IV（安全风险）");
    println!("   ❌ 不支持不同密钥");
    println!("   ❌ 固定IV导致安全漏洞");
    
    println!("\n   我们的实现优点:");
    println!("   ✅ 灵活的密钥管理");
    println!("   ✅ 安全的随机IV");
    println!("   ✅ 详细的错误信息");
    println!("   ✅ 符合密码学最佳实践");
    
    // 5. 改进建议
    println!("\n💡 **改进建议**");
    println!("   如果要改进你的实现，建议:");
    println!("   1. 使用随机IV而不是固定IV");
    println!("   2. 将密钥作为参数传递");
    println!("   3. 返回IV和密文的组合");
    println!("   4. 考虑使用AES-GCM模式（提供认证）");
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_fixed_iv_problem() {
        let plaintext = b"Test message";
        
        // 你的实现 - 固定IV问题
        let cipher1 = enc(plaintext).unwrap();
        let cipher2 = enc(plaintext).unwrap();
        
        // 相同明文产生相同密文 - 这是安全问题！
        assert_eq!(cipher1, cipher2);
    }
    
    #[test]
    fn test_random_iv_security() {
        let key = b"test_key_16bytes";
        let plaintext = b"Test message";
        
        // 我们的实现 - 随机IV
        let (iv1, cipher1) = our_encrypt(key, plaintext);
        let (iv2, cipher2) = our_encrypt(key, plaintext);
        
        // 不同的IV和密文 - 这是安全的！
        assert_ne!(iv1, iv2);
        assert_ne!(cipher1, cipher2);
        
        // 但解密结果应该相同
        let decrypted1 = our_decrypt(key, &iv1.try_into().unwrap(), &cipher1).unwrap();
        let decrypted2 = our_decrypt(key, &iv2.try_into().unwrap(), &cipher2).unwrap();
        
        assert_eq!(decrypted1, plaintext);
        assert_eq!(decrypted2, plaintext);
    }
}
