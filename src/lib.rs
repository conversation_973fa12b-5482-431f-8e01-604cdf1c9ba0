use aes::Aes128;
use cbc::{Decryptor, Encryptor};
use cbc::cipher::{KeyIvInit, BlockDecryptMut, BlockEncryptMut, block_padding::Pkcs7};
use rand::Rng;

type Aes128CbcEnc = Encryptor<Aes128>;
type Aes128CbcDec = Decryptor<Aes128>;

/// AES-128-CBC 加密函数
pub fn aes128_cbc_encrypt(key: &[u8; 16], plaintext: &[u8]) -> (Vec<u8>, Vec<u8>) {
    let mut iv = [0u8; 16];
    rand::rng().fill(&mut iv);
    
    let cipher = Aes128CbcEnc::new(key.into(), &iv.into());
    let mut buffer = vec![0u8; plaintext.len() + 16];
    buffer[..plaintext.len()].copy_from_slice(plaintext);
    
    let ciphertext_len = cipher.encrypt_padded_mut::<Pkcs7>(&mut buffer, plaintext.len())
        .expect("加密失败").len();
    
    buffer.truncate(ciphertext_len);
    (iv.to_vec(), buffer)
}

/// AES-128-CBC 解密函数
pub fn aes128_cbc_decrypt(key: &[u8; 16], iv: &[u8; 16], ciphertext: &[u8]) -> Result<Vec<u8>, String> {
    let cipher = Aes128CbcDec::new(key.into(), iv.into());
    let mut buffer = ciphertext.to_vec();
    
    let plaintext = cipher.decrypt_padded_mut::<Pkcs7>(&mut buffer)
        .map_err(|e| format!("解密失败: {:?}", e))?;
    
    Ok(plaintext.to_vec())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_encrypt_decrypt_basic() {
        let key = b"my_secret_key123";
        let plaintext = b"Hello, World!";
        
        let (iv, ciphertext) = aes128_cbc_encrypt(key, plaintext);
        let iv_array: [u8; 16] = iv.try_into().unwrap();
        let decrypted = aes128_cbc_decrypt(key, &iv_array, &ciphertext).unwrap();
        
        assert_eq!(plaintext.to_vec(), decrypted);
    }

    #[test]
    fn test_different_lengths() {
        let key = b"test_key_16bytes";
        let test_cases = vec![
            b"A".as_slice(),                    // 1字节
            b"Hello World!",                    // 12字节
            b"This is exactly 16!",             // 19字节
            b"This message is longer than 16 bytes", // 36字节
        ];
        
        for plaintext in test_cases {
            let (iv, ciphertext) = aes128_cbc_encrypt(key, plaintext);
            let iv_array: [u8; 16] = iv.try_into().unwrap();
            let decrypted = aes128_cbc_decrypt(key, &iv_array, &ciphertext).unwrap();
            
            assert_eq!(plaintext.to_vec(), decrypted);
            // 验证密文长度是16的倍数
            assert_eq!(ciphertext.len() % 16, 0);
        }
    }

    #[test]
    fn test_iv_randomness() {
        let key = b"test_key_16bytes";
        let plaintext = b"Same message";
        
        let (iv1, ciphertext1) = aes128_cbc_encrypt(key, plaintext);
        let (iv2, ciphertext2) = aes128_cbc_encrypt(key, plaintext);
        
        // IV应该不同
        assert_ne!(iv1, iv2);
        // 密文应该不同
        assert_ne!(ciphertext1, ciphertext2);
    }

    #[test]
    fn test_invalid_ciphertext_length() {
        let key = b"test_key_16bytes";
        let iv = [0u8; 16];
        let invalid_ciphertext = vec![0u8; 15]; // 不是16的倍数
        
        let result = aes128_cbc_decrypt(key, &iv, &invalid_ciphertext);
        assert!(result.is_err());
    }

    #[test]
    fn test_empty_plaintext() {
        let key = b"test_key_16bytes";
        let plaintext = b"";
        
        let (iv, ciphertext) = aes128_cbc_encrypt(key, plaintext);
        let iv_array: [u8; 16] = iv.try_into().unwrap();
        let decrypted = aes128_cbc_decrypt(key, &iv_array, &ciphertext).unwrap();
        
        assert_eq!(plaintext.to_vec(), decrypted);
        // 空明文应该产生16字节密文（全填充块）
        assert_eq!(ciphertext.len(), 16);
    }
}
