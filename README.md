# AES-128-CBC 加解密学习项目

这个项目演示了如何在Rust中使用第三方crate实现AES-128-CBC加解密，包含详细的原理讲解和实际示例。

## 🔐 AES-128-CBC 原理

### 基本概念
- **AES-128**: 高级加密标准，使用128位(16字节)密钥
- **CBC模式**: 密码块链接模式，每个块的加密依赖于前一个密文块
- **PKCS#7填充**: 自动处理数据块对齐的填充算法

### 加密流程
```
明文块1 ⊕ IV → AES加密 → 密文块1
明文块2 ⊕ 密文块1 → AES加密 → 密文块2
明文块3 ⊕ 密文块2 → AES加密 → 密文块3
...
```

### 解密流程
```
密文块1 → AES解密 → ⊕ IV → 明文块1
密文块2 → AES解密 → ⊕ 密文块1 → 明文块2
密文块3 → AES解密 → ⊕ 密文块2 → 明文块3
...
```

## 📦 使用的Crate

```toml
[dependencies]
aes = "0.8.4"      # AES算法实现
cbc = "0.1.2"      # CBC模式实现
hex = "0.4.3"      # 十六进制编码
rand = "0.9.2"     # 随机数生成
```

## 🚀 核心API

### 加密函数
```rust
fn aes128_cbc_encrypt(key: &[u8; 16], plaintext: &[u8]) -> (Vec<u8>, Vec<u8>)
```
- **输入**: 16字节密钥 + 任意长度明文
- **输出**: (IV, 密文) 元组
- **特点**: 自动生成随机IV，自动PKCS#7填充

### 解密函数
```rust
fn aes128_cbc_decrypt(key: &[u8; 16], iv: &[u8; 16], ciphertext: &[u8]) -> Result<Vec<u8>, String>
```
- **输入**: 16字节密钥 + 16字节IV + 密文
- **输出**: 解密后的明文或错误
- **特点**: 自动去除PKCS#7填充

## 🔍 关键特性

### 1. PKCS#7填充
- 自动将数据填充到16字节的倍数
- 填充字节的值等于填充的字节数
- 例如：需要3字节填充 → 添加 `[03, 03, 03]`

### 2. 随机IV
- 每次加密都生成新的随机IV
- 确保相同明文产生不同密文
- IV可以公开，但不能重复使用

### 3. 错误处理
- 密文长度必须是16字节的倍数
- 填充必须有效（PKCS#7规范）
- 提供详细的错误信息

## 📋 运行示例

### 基本演示
```bash
cargo run
```

### 运行测试
```bash
cargo test
```

## 🎯 学习要点

### 安全最佳实践
1. **密钥管理**: 使用安全的密钥生成和存储
2. **IV唯一性**: 每次加密使用不同的随机IV
3. **填充攻击**: 正确处理填充验证错误
4. **侧信道攻击**: 注意时间攻击等安全问题

### 性能考虑
1. **内存分配**: 预分配足够的缓冲区空间
2. **批量处理**: 对大量数据考虑流式处理
3. **硬件加速**: 现代CPU的AES-NI指令集支持

### 常见陷阱
1. **IV重用**: 绝不能用相同的密钥+IV组合
2. **填充预言**: 不要泄露填充验证信息
3. **密钥派生**: 不要直接使用密码作为密钥

## 🔧 代码结构

```
src/
├── main.rs      # 主演示程序
├── lib.rs       # 核心加解密函数和测试
├── examples.rs  # 详细示例和教学演示
└── README.md    # 本文档
```

## 📚 进一步学习

1. **其他AES模式**: GCM、CTR、ECB等
2. **密钥派生**: PBKDF2、scrypt、Argon2
3. **消息认证**: HMAC、GCM认证加密
4. **非对称加密**: RSA、ECC等公钥算法

## ⚠️ 免责声明

本项目仅用于学习目的。在生产环境中使用加密算法时，请：
- 进行专业的安全审计
- 遵循最新的安全标准
- 考虑使用经过验证的加密库
- 实施完整的密钥管理方案
